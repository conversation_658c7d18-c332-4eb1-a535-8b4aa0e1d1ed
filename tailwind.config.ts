import type { Config } from "tailwindcss";

const config: Config = {
  darkMode: ["class"],
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/features/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      fontFamily: {
        poppins: ["var(--font-poppins)"],
        inter: ["var(--font-inter)"],
      },
      size: {
        "icon-res-base": "1.25vw",
        "icon-res-lg": "1.5vw",
      },
      spacing: {
        "res-x-xs": "0.5vw",
        "res-x-sm": "1vw",
        "res-x-base": "1.5vw",
        "res-x-lg": "2vw",
        "res-x-xl": "2.5vw",
        "res-x-2xl": "3vw",
        "res-x-3xl": "3.5vw",
        "res-x-4xl": "4vw",
        "res-y-xs": "0.5vh",
        "res-y-sm": "1vh",
        "res-y-base": "1.5vh",
        "res-y-lg": "2vh",
        "res-y-xl": "2.5vh",
        "res-y-2xl": "3vh",
        "res-y-3xl": "3.5vh",
        "res-y-4xl": "4vh",
      },
      backgroundImage: {
        gradient: "linear-gradient(to bottom, #193798, #020b77)",
      },
      colors: {
        background: "#f4f4f4",
        foreground: "var(--foreground)",
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        primary: {
          50: "var(--primary-50)",
          100: "var(--primary-100)",
          200: "var(--primary-200)",
          300: "var(--primary-300)",
          400: "var(--primary-400)",
          450: "var(--primary-450)",
          500: "var(--primary-500)",
          600: "var(--primary-600)",
          700: "var(--primary-700)",
          800: "var(--primary-800)",
          900: "var(--primary-900)",
          950: "var(--primary-950)",
        },
        secondary: {
          "50": "var(--secondary-50)",
          "100": "var(--secondary-100)",
          "200": "var(--secondary-200)",
          "300": "var(--secondary-300)",
          "400": "var(--secondary-400)",
          "500": "var(--secondary-500)",
          "600": "var(--secondary-600)",
          "700": "var(--secondary-700)",
          "800": "var(--secondary-800)",
          "900": "var(--secondary-900)",
          "950": "var(--secondary-950)",
        },
        green: {
          "50": "var(--green-50)",
          "100": "var(--green-100)",
          "200": "var(--green-200)",
          "300": "var(--green-300)",
          "400": "var(--green-400)",
          "500": "var(--green-500)",
          "600": "var(--green-600)",
          "700": "var(--green-700)",
          "800": "var(--green-800)",
          "900": "var(--green-900)",
          "950": "var(--green-950)",
        },
        neutral: {
          "50": "var(--neutral-50)",
          "100": "var(--neutral-100)",
          "200": "var(--neutral-200)",
          "300": "var(--neutral-300)",
          "400": "var(--neutral-400)",
          "500": "var(--neutral-500)",
          "600": "var(--neutral-600)",
          "700": "var(--neutral-700)",
          "800": "var(--neutral-800)",
          "900": "var(--neutral-900)",
          "950": "var(--neutral-950)",
        },
        font: {
          primary: "var(--font-primary)",
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      keyframes: {
        "accordion-down": {
          from: {
            height: "0",
          },
          to: {
            height: "var(--radix-accordion-content-height)",
          },
        },
        "accordion-up": {
          from: {
            height: "var(--radix-accordion-content-height)",
          },
          to: {
            height: "0",
          },
        },
        progress: {
          "0%": { transform: "translateX(0) scaleX(0)" },
          "40%": { transform: "translateX(0) scaleX(0.4)" },
          "100%": { transform: "translateX(100%) scaleX(0.5)" },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
        progress: "progress 1s infinite linear",
      },
    },
  },
  plugins: [require("tailwindcss-animate"), require("tailwind-scrollbar")],
};
export default config;
