/** @type {import('next').NextConfig} */
const nextConfig = {
  // Add API proxy for local development only to avoid CORS issues
  async rewrites() {
    // Only enable proxy for local development (when using localhost URLs)
    const isLocalDevelopment =
      process.env.NODE_ENV === "development" &&
      process.env.NEXT_PUBLIC_API_URL?.includes("localhost");

    // Get the actual backend URL from environment variable
    const backendUrl = process.env.PROXY_BACKEND_URL || process.env.NEXT_PUBLIC_BACKEND_URL;

    if (!backendUrl && isLocalDevelopment) {
      console.warn("Warning: PROXY_BACKEND_URL not set for local development proxy");
      return [];
    }

    return isLocalDevelopment && backendUrl
      ? [
          {
            source: "/api/v1/:path*",
            destination: `${backendUrl}/v1/:path*`,
          },
        ]
      : [];
  },
  webpack(config) {
    // Grab the existing rule that handles SVG imports
    const fileLoaderRule = config.module.rules.find((rule) =>
      rule.test?.test?.(".svg")
    );

    config.module.rules.push(
      // Reapply the existing rule, but only for svg imports ending in ?url
      {
        ...fileLoaderRule,
        test: /\.svg$/i,
        resourceQuery: /url/, // *.svg?url
      },
      // Convert all other *.svg imports to React components
      {
        test: /\.svg$/i,
        issuer: fileLoaderRule.issuer,
        resourceQuery: { not: [...fileLoaderRule.resourceQuery.not, /url/] }, // exclude if *.svg?url
        use: ["@svgr/webpack"],
      }
    );

    // Modify the file loader rule to ignore *.svg, since we have it handled now.
    fileLoaderRule.exclude = /\.svg$/i;

    return config;
  },
};

export default nextConfig;
