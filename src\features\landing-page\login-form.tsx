"use client";

import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import Link from "next/link";

import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input/";
import { PasswordInput } from "@/components/ui/input/password-input";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { IconAlertCircle } from "@tabler/icons-react";
import { useRouter } from "next/navigation";
import { useLogin } from "@/features/auth/api/login";
import { useState } from "react";
import { isRequestError } from "@/lib/api-client";
import { PATH } from "@/constants/path";
import { useIsHydrated } from "@/lib/hooks/use-hydrated";

import { FormContainer } from "./form-base-layout";

const formSchema = z.object({
  organization_unique_id: z
    .string()
    .min(1, { message: "Please fill out the organization ID" }),
  email: z.string().email(),
  password: z.string().min(8, "Password minimum contain 8 characters"),
  remember_me: z.boolean().optional(),
});

type FormSchema = z.infer<typeof formSchema>;

export const LoginForm = ({
  className,
}: React.HTMLAttributes<HTMLFormElement> & { organizationId?: number }) => {
  const router = useRouter();
  const { isHydrated } = useIsHydrated();

  const form = useForm<FormSchema>({
    resolver: zodResolver(formSchema),
    reValidateMode: "onSubmit",
    defaultValues: {
      email: "",
      organization_unique_id: "",
      password: "",
      remember_me: false,
    },
  });

  const loginMutation = useLogin({
    mutationConfig: {
      notification: true,
    },
  });

  const [errorNotif, setErrorNotif] = useState<string>("");

  const onSubmit = async (values: FormSchema) => {
    try {
      await loginMutation.mutateAsync(values);

      router.push(PATH.DASHBOARD);
    } catch (e) {
      if (isRequestError(e)) {
        // Follow the established pattern used throughout the codebase
        const errorMessage = e.response?.data.errors[0]?.message ?? "";
        setErrorNotif(
          errorMessage ||
            "An error occurred while logging in. Please try again."
        );
      } else {
        setErrorNotif("An error occurred while logging in. Please try again.");
      }
    }
  };

  if (!isHydrated) return null;

  return (
    <Form {...form}>
      <FormContainer
        className={className}
        onSubmit={form.handleSubmit(onSubmit)}
      >
        <div className="mb-4">
          <p className="text-4xl font-bold">Welcome back</p>
          <p className="mt-2 text-lg font-thin">Please enter your details.</p>
        </div>

        <div className="mb-2 grid gap-6">
          {errorNotif && (
            <Alert variant="destructive" className="relative mb-4">
              <IconAlertCircle className="size-4" />
              <AlertTitle className="font-bold">Login Failed</AlertTitle>
              <AlertDescription>{errorNotif}</AlertDescription>
            </Alert>
          )}
          <FormField
            control={form.control}
            name="organization_unique_id"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <Input
                    id="organization_unique_id"
                    placeholder="Enter your organization ID"
                    className="h-12 w-full"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <Input
                    id="email"
                    type="email"
                    placeholder="Enter your email"
                    className="h-12 w-full"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <PasswordInput
                    id="password"
                    className="h-12 w-full"
                    placeholder="Enter your password"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <Link href={PATH.FORGOT_PASSWORD}>
            <p className="cursor-pointer pl-2 text-sm font-semibold text-secondary-500">
              Forgot password &#x3f;
            </p>
          </Link>

          <div className="flex items-center justify-between text-primary-500">
            <FormField
              control={form.control}
              name="remember_me"
              render={({ field }) => (
                <FormItem className="flex items-center gap-2 space-y-0">
                  <FormControl>
                    <Checkbox
                      checked={field.value}
                      onCheckedChange={(checked) => field.onChange(checked)}
                    />
                  </FormControl>
                  <FormLabel>Remember Me</FormLabel>
                </FormItem>
              )}
            />
          </div>

          <Alert className="bg-neutral-200 p-2 px-4">
            <AlertDescription>
              By logging in, I agree to Perception Selling's{" "}
              <Link
                className="font-medium underline hover:font-semibold"
                href={PATH.TERMS_OF_SERVICE}
              >
                Terms of Service
              </Link>{" "}
              and{" "}
              <Link
                className="font-medium underline hover:font-semibold"
                href={PATH.PRIVACY_POLICY}
              >
                Privacy Policy
              </Link>
              .
            </AlertDescription>
          </Alert>
        </div>

        <Button
          className="w-full bg-gradient"
          isLoading={loginMutation.isPending}
        >
          Log in
        </Button>
      </FormContainer>
    </Form>
  );
};
