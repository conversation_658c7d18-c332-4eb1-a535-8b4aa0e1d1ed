import { type ColumnDef } from "@tanstack/react-table";
import { useParams } from "next/navigation";
import { useCallback, useEffect, useMemo, useState } from "react";
import _ from "lodash";
import { toast } from "sonner";

import {
  AccountPlanData,
  AccountPlanTableType,
} from "@/features/account-plan/types";
import DataTable, { DataTableMeta } from "@/components/ui/data-table";
import {
  DatePickerCell,
  EditableNumberCell,
  TiptapCell,
} from "@/components/ui/data-table/data-table-components";
import { APPotentialOpportunity } from "@/features/account-plan/types/revenue-types";
import { Button } from "@/components/ui/button";
import { useCreatePotentialOpportunity } from "@/features/account-plan/api/revenue-apis/potential-opportunity/create-potential-opportunity";
import { useUpdatePotentialOpportunity } from "@/features/account-plan/api/revenue-apis/potential-opportunity/update-potential-opportunity";
import { useDeletePotentialOpportunity } from "@/features/account-plan/api/revenue-apis/potential-opportunity/delete-potential-opportunity";
import { usePotentialOpportunityList } from "@/features/account-plan/api/revenue-apis/potential-opportunity/get-potential-opportunity-list";
import { getAccountPlanTableName } from "@/features/account-plan/constants";

import { AccountTable } from "../base-table";

export const PotentialOpportunitiesTable = ({
  accountPlan,
}: {
  accountPlan?: AccountPlanData;
}) => {
  const [tableData, setTableData] = useState<APPotentialOpportunity[]>([]);
  const [rowSelection, setRowSelection] = useState<Record<string, boolean>>({});

  const { id } = useParams<{ id: string }>();
  const accountId = parseInt(id);

  const { potentialOpportunityList } = usePotentialOpportunityList({
    accountId,
    params: {
      disable_pagination: true,
    },
  });
  const createPotentialOpportunity = useCreatePotentialOpportunity({});
  const updatePotentialOpportunity = useUpdatePotentialOpportunity({
    mutationConfig: {
      invalidate: true,
    },
  });
  const deletePotentialOpportunity = useDeletePotentialOpportunity({});

  useEffect(() => {
    if (!potentialOpportunityList) return;

    const newTableData = potentialOpportunityList?.map((v, idx) => ({
      idx,
      ...v,
    }));

    setTableData(newTableData);
  }, [potentialOpportunityList]);

  const selectedRows = Object.keys(rowSelection)
    .filter((rowId) => rowSelection[rowId])
    .map((idx) => tableData[parseInt(idx)]);

  const onAddRow = async () => {
    try {
      const res = await createPotentialOpportunity.mutateAsync({
        accountId,
      });

      setTableData((prev) => [...prev, res.data]);
    } catch (_) {
      toast("An unexpected error occured when adding data");
    }
  };

  const onDeleteRows = async () => {
    try {
      const promises = [];

      setTableData(
        tableData.filter((row) => !selectedRows.find((v) => v.id === row.id))
      );

      setRowSelection({});

      promises.push(
        selectedRows.map(async (row) => {
          if (!!row?.id) {
            return deletePotentialOpportunity.mutateAsync({
              id: row.id,
              accountId,
            });
          }
        })
      );
      await Promise.all(promises);
    } catch (_) {
      toast("An unexpected error occured when deleting rows");
    }
  };

  const onChangeData = useCallback(
    async (data: Partial<APPotentialOpportunity>, id: number) => {
      try {
        setTableData((prev) =>
          prev.map((v) => (v.id === id ? { ...v, ...data } : v))
        );

        await updatePotentialOpportunity.mutateAsync({
          accountId,
          id,
          data,
        });
      } catch (_) {
        toast("An unexpected error occured when modifying data");
      }
    },
    [accountId, updatePotentialOpportunity]
  );

  const columns: ColumnDef<APPotentialOpportunity>[] = useMemo(
    () => [
      {
        accessorKey: "product_service_name",
        header: "Service and Product Description",
        size: 575,
        cell: ({ row, table }) => {
          const isPreview =
            (table.options.meta as DataTableMeta)?.isPreview ?? false;
          const rowData = row.original;

          return (
            <TiptapCell
              value={rowData.product_service_name}
              onChange={(product_service_name) =>
                onChangeData({ product_service_name }, rowData.id)
              }
              placeholder="Input service and product description"
              className={isPreview ? "text-4xl" : "text-[16px]"}
            />
          );
        },
        meta: {
          tooltip:
            "The products or services that could be introduced to the account as part of cross-sell or up-sell strategies.",
          padding: true,
        },
      },
      {
        accessorKey: "value",
        header: "Currency Value",
        size: 200,
        cell: ({ row, table }) => {
          const isPreview =
            (table.options.meta as DataTableMeta)?.isPreview ?? false;
          const rowData = row.original;

          return (
            <EditableNumberCell
              value={rowData.value}
              onChange={(value) => {
                onChangeData({ value }, rowData.id);
              }}
              className={isPreview ? "text-4xl" : "text-[16px]"}
            />
          );
        },
        meta: {
          tooltip:
            "The projected monetary value of the cross-sell or up-sell opportunity, reflecting its potential impact on revenue.",
          padding: true,
        },
      },
      {
        accessorKey: "close_date",
        header: "Close Date",
        size: 225,
        cell: ({ row, table }) => {
          const isPreview =
            (table.options.meta as DataTableMeta)?.isPreview ?? false;
          const rowData = row.original;

          return (
            <DatePickerCell
              className={isPreview ? "text-4xl" : "text-[16px]"}
              date={rowData.close_date}
              onSelect={(close_date) => {
                onChangeData(
                  { close_date: close_date as unknown as string },
                  rowData.id
                );
              }}
            />
          );
        },
        meta: {
          tooltip:
            "The anticipated date by which the cross-sell or up-sell opportunity is expected to close or be finalized.",
          padding: true,
        },
      },
    ],
    [onChangeData]
  );

  return (
    <AccountTable
      type={AccountPlanTableType.POTENTIAL_OPPORTUNITY}
      footer={
        <>
          <Button
            onClick={onAddRow}
            isLoading={createPotentialOpportunity.isPending}
          >
            Add row
          </Button>
          <Button
            variant="destructive"
            disabled={selectedRows.length === 0}
            onClick={onDeleteRows}
          >
            Delete Row
          </Button>
        </>
      }
    >
      <DataTable
        columns={columns}
        data={tableData}
        rowSelection={rowSelection}
        setRowSelection={setRowSelection}
        currency={accountPlan?.account_plan_group?.currency}
        tableType={AccountPlanTableType.POTENTIAL_OPPORTUNITY}
      />
    </AccountTable>
  );
};
