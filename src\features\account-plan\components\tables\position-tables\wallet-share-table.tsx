import { type ColumnDef } from "@tanstack/react-table";
import { useParams } from "next/navigation";
import { useCallback, useEffect, useMemo, useState } from "react";
import _ from "lodash";
import { toast } from "sonner";

import {
  AccountPlanData,
  AccountPlanTableType,
} from "@/features/account-plan/types";
import {
  APWalletShare,
  APWalletShareType,
} from "@/features/account-plan/types/position-types";
import DataTable, { DataTableMeta } from "@/components/ui/data-table";
import {
  EditableNumberCell,
  TiptapCell,
} from "@/components/ui/data-table/data-table-components";
import { useWalletShareList } from "@/features/account-plan/api/position-apis/wallet-share/get-wallet-share-list";
import { useUpdateWalletShare } from "@/features/account-plan/api/position-apis/wallet-share/update-wallet-share";
import { cn } from "@/lib/utils";
import {
  Pop<PERSON>,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

import { AccountTable } from "../base-table";
import { useAvailableWalletSize } from "../hooks/use-available-wallet";

export const walletShareOptions = {
  [APWalletShareType.ADDRESSABLE]: {
    order: 1,
    name: "Wallet Size",
    description:
      "The total addressable wallet size is their total capacity to spend with us and our competition within any given budgeting year. Includes what they spend with us; what they spend with the competition and any surplus it is believed exists.",
  },
  [APWalletShareType.OURS]: {
    order: 2,
    name: "Existing",
    description:
      "The total amount they spend on our services in any given budgeting year.",
  },
  [APWalletShareType.COMPETITION]: {
    order: 3,
    name: "Competition",
    description:
      "The total amount they spend with all the competition in any given budgeting year.",
  },
  [APWalletShareType.AVAILABLE]: {
    order: 4,
    name: "Available",
    description:
      "The potential they could spend on ours and/or the competition’s product or services in any given budgeting year. (Total Addressable – Ours - Competition = Available)",
  },
} as const;

const WalletSizeTypeTooltip = ({
  isPreview,
  name,
  description,
  className,
}: {
  isPreview: boolean;
  name: string;
  description: string;
  className?: string;
}) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <Popover open={isOpen}>
      <PopoverTrigger
        className={cn("text-center font-bold", className)}
        onMouseLeave={() => setIsOpen(false)}
        onMouseEnter={() => setIsOpen(true)}
      >
        {name}
      </PopoverTrigger>
      <PopoverContent
        align="start"
        side="bottom"
        className={cn(
          "flex-wrap whitespace-normal text-start font-semibold",
          isPreview ? "w-[35vw] text-lg" : "w-[55vw] text-2xl",
          className
        )}
      >
        {" "}
        {description}
      </PopoverContent>
    </Popover>
  );
};

export const WalletShareTable = ({
  accountPlan,
}: {
  accountPlan?: AccountPlanData;
}) => {
  const [tableData, setTableData] = useState<APWalletShare[]>([]);

  const { id } = useParams<{ id: string }>();
  const accountId = parseInt(id);

  const { totalCurrentRevenue } = useAvailableWalletSize();
  const { walletShareList } = useWalletShareList({
    accountId,
    params: {
      disable_pagination: true,
    },
  });
  const updateWalletShare = useUpdateWalletShare({
    mutationConfig: {
      invalidate: true,
    },
  });

  useEffect(() => {
    if (!walletShareList) return;

    const newTableData = walletShareList
      ?.map((v, idx) => ({
        idx,
        ...v,
      }))
      .sort(
        (a, b) =>
          walletShareOptions[a.item_type].order -
          walletShareOptions[b.item_type].order
      );
    setTableData(newTableData);
  }, [walletShareList]);

  const onChangeData = useCallback(
    async (data: Partial<APWalletShare>, id: number) => {
      try {
        setTableData((prev) =>
          prev.map((v) => (v.id === id ? { ...v, ...data } : v))
        );
        await updateWalletShare.mutateAsync({
          accountId,
          id,
          data,
        });
      } catch (_) {
        toast("An unexpected error occured when modifying data");
      }
    },
    [accountId, updateWalletShare]
  );

  const columns: ColumnDef<APWalletShare>[] = useMemo(() => {
    if (tableData.length === 0) return [];
    const addressableValue = tableData[0].shared_type_analysis ?? 0;
    const existingValue = tableData[1].shared_type_analysis ?? 0;
    const competitionValue = tableData[2].shared_type_analysis ?? 0;
    const availableValue = addressableValue - existingValue - competitionValue;

    return [
      {
        accessorKey: "item_type",
        header: "",
        size: 70,
        cell: ({ row, table }) => {
          const rowData = row.original;
          const option = walletShareOptions[rowData.item_type];
          const isPreview =
            (table.options.meta as DataTableMeta)?.isPreview ?? false;

          return (
            <WalletSizeTypeTooltip
              className={isPreview ? "text-4xl" : "text-[16px]"}
              isPreview={isPreview}
              name={option.name}
              description={option.description}
            />
          );
        },
        meta: {
          padding: true,
        },
      },
      {
        accessorKey: "shared_type_analysis_percentage",
        header: "%",
        size: 50,
        cell: ({ row, table }) => {
          const isPreview =
            (table.options.meta as DataTableMeta)?.isPreview ?? false;
          const rowData = row.original;

          const value =
            rowData.item_type === APWalletShareType.AVAILABLE
              ? availableValue
              : (rowData.shared_type_analysis ?? 0);

          const percentage =
            addressableValue === 0 ? 0 : (value / addressableValue) * 100;
          return (
            <div className={isPreview ? "text-4xl" : "text-[16px]"}>
              {percentage.toFixed(0)}%
            </div>
          );
        },
        meta: {
          padding: true,
        },
      },
      {
        accessorKey: "shared_type_analysis",
        header: "Currency Value",
        size: 50,
        cell: ({ row, table }) => {
          const isPreview =
            (table.options.meta as DataTableMeta)?.isPreview ?? false;
          const rowData = row.original;
          const isAvailableWallet =
            rowData.item_type === APWalletShareType.AVAILABLE;
          const isExistingWallet = rowData.item_type === APWalletShareType.OURS;
          const isAddressableWallet =
            rowData.item_type === APWalletShareType.ADDRESSABLE;

          const value = isAvailableWallet
            ? availableValue
            : (rowData.shared_type_analysis ?? 0);

          return (
            <EditableNumberCell
              className={`${isPreview ? "text-4xl" : "text-[16px]"} disabled:border-none disabled:text-black disabled:opacity-100`}
              disabled={isAvailableWallet}
              value={value}
              minValue={
                isAddressableWallet
                  ? existingValue + competitionValue
                  : isExistingWallet
                    ? totalCurrentRevenue
                    : undefined
              }
              handleMinErrorMessage={
                isAddressableWallet
                  ? (val) =>
                      `Value cannot be less than ${val} (Existing + Competition Value)`
                  : isExistingWallet
                    ? (val) =>
                        `Value cannot be less than ${val} (Total Existing Revenue)`
                    : undefined
              }
              maxValue={
                isAddressableWallet ? undefined : availableValue + value
              }
              handleMaxErrorMessage={
                isAddressableWallet
                  ? undefined
                  : (val) =>
                      `Value cannot exceed ${val} (Available Wallet Size)`
              }
              onChange={(shared_type_analysis) => {
                onChangeData({ shared_type_analysis }, rowData.id);
              }}
            />
          );
        },
        meta: {
          tooltip:
            "The monetary value associated with the wallet share. The currency is automatically pulled from the account parameters to ensure consistency.",
          padding: true,
        },
      },
      {
        accessorKey: "desc",
        header: "Service and Product Description",
        size: 180,
        cell: ({ row, table }) => {
          const isPreview =
            (table.options.meta as DataTableMeta)?.isPreview ?? false;
          const rowData = row.original;
          const isWithoutDescription =
            rowData.item_type === APWalletShareType.ADDRESSABLE ||
            rowData.item_type === APWalletShareType.AVAILABLE;

          return (
            <TiptapCell
              editable={!isWithoutDescription}
              className={cn(
                isPreview ? "text-4xl" : "text-[16px]",
                isWithoutDescription && "cursor-not-allowed bg-neutral-50"
              )}
              value={isWithoutDescription ? "" : rowData.description}
              onChange={(description) =>
                onChangeData({ description }, rowData.id)
              }
              placeholder={
                isWithoutDescription
                  ? "-"
                  : "Input service and product description"
              }
            />
          );
        },
        meta: {
          tooltip:
            "Additional details or contextual notes about the wallet share, including its significance or relevant observations.",
          padding: true,
        },
      },
    ];
  }, [onChangeData, tableData, totalCurrentRevenue]);

  return (
    <AccountTable type={AccountPlanTableType.WALLET_SHARE}>
      <DataTable
        columns={columns}
        data={tableData}
        currency={accountPlan?.account_plan_group?.currency}
        tableType={AccountPlanTableType.WALLET_SHARE}
      />
    </AccountTable>
  );
};
