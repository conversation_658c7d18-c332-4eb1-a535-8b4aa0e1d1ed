"use client";

import Image from "next/image";

import PsAi<PERSON>ogo from "@/assets/logo.png";
import { SidebarMenu } from "@/components/layout/dashboard";
import { AccountPlanSideMenu } from "@/features/account-plan/components/side-menu";
import { PATH } from "@/constants/path";
import { usePathname } from "next/navigation";
import { Suspense, useMemo } from "react";
import { cn } from "@/lib/utils";

export default function DashboardLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const pathname = usePathname();

  const matchedMenu = useMemo(() => {
    const sideMenu = [
      {
        path: `^${PATH.DASHBOARD_ACCOUNT_PLAN}/[0-9]+$`,
        menu: <AccountPlanSideMenu />,
        innerContainerClass: "overflow-y-hidden !mt-0",
      },
    ];

    return sideMenu.find((v) => new RegExp(v.path).test(pathname));
  }, [pathname]);

  return (
    <div className="flex h-screen w-full overflow-hidden">
      <aside className="z-50 hidden h-full max-h-screen w-[15%] shrink-0 flex-col items-center gap-res-y-4xl overflow-hidden rounded-e-[1vw] bg-white px-res-x-base py-res-y-lg text-font-primary transition-all ease-in-out md:block lg:flex">
        <Image src={PsAiLogo} alt="ps-ai-logo" />
        <SidebarMenu />
      </aside>
      {matchedMenu?.menu}
      <main className="-ml-res-x-sm flex max-h-screen w-full flex-col overflow-auto bg-zinc-100">
        <div
          className={cn(
            "mx-auto my-res-y-3xl w-full pl-res-x-3xl pr-res-x-lg",
            matchedMenu?.innerContainerClass
          )}
        >
          <Suspense fallback={null}>{children}</Suspense>
        </div>
      </main>
    </div>
  );
}
