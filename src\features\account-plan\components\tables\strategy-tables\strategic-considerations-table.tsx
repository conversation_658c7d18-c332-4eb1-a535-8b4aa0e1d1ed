import { type ColumnDef } from "@tanstack/react-table";
import { useParams } from "next/navigation";
import { useMemo } from "react";
import _ from "lodash";
import { toast } from "sonner";

import { AccountPlanTableType } from "@/features/account-plan/types";
import DataTable, { DataTableMeta } from "@/components/ui/data-table";
import { APActionPlan } from "@/features/account-plan/types/strategy-types";
import { TiptapCell } from "@/components/ui/data-table/data-table-components";
import { useGenerateActionPlan } from "@/features/account-plan/api/strategy-apis/action-plan/create-action-plan-generate";
import { useActionPlanList } from "@/features/account-plan/api/strategy-apis/action-plan/get-action-plan-list";

import { AccountTable, AccountTableTitle } from "../base-table";
import { getAccountPlanTableName } from "@/features/account-plan/constants";

export const StrategicConsiderationTable = () => {
  const { id } = useParams<{ id: string }>();
  const accountId = parseInt(id);

  const { actionPlanList } = useActionPlanList({
    accountId,
    params: {
      disable_pagination: true,
    },
  });
  const generateActionPlan = useGenerateActionPlan({});

  const onGenerateActionPlan = async () => {
    try {
      await generateActionPlan.mutateAsync({
        accountId,
      });

      toast("Successfully generated the strategic consideration");
    } catch (_) {
      toast("An error occured while generating the strategic consideration");
    }
  };

  const columns: ColumnDef<APActionPlan>[] = useMemo(
    () => [
      {
        accessorKey: "description",
        header: "",
        cell: ({ row, table }) => {
          const isPreview =
            (table.options.meta as DataTableMeta)?.isPreview ?? false;
          const rowData = row.original;

          return (
            <TiptapCell
              value={rowData.description ?? ""}
              onChange={() => {}}
              editable={false}
              className={isPreview ? "text-4xl px-3 py-2 px-res-x-sm py-res-y-base" : "text-[16px] px-3 py-2 px-res-x-sm py-res-y-base"}
            />
          );
        },
      },
    ],
    []
  );

  return (
    <AccountTable
      type={AccountPlanTableType.ACTION_PLAN}
      heightRatio={0.2}
      onGenerate={onGenerateActionPlan}
      isLoading={generateActionPlan.isPending}
    >
      <DataTable
        columns={columns}
        data={actionPlanList ?? []}
        headerClassName="!text-center"
        showHeader={false}
        tableType={AccountPlanTableType.ACTION_PLAN}
        emptyMessage={
          <p className="mt-4 text-3xl text-neutral-300">
            Generate analysis to view Strategic Considerations. <br /> Please
            ensure all the data for position and revenue tables <br /> has been
            inputted to achieve more accurate results.
          </p>
        }
      />
    </AccountTable>
  );
};
