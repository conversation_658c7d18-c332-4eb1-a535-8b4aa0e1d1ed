import React, { useState } from "react";
import {
  Command,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";

import { useAccountPlanList } from "../api/get-account-plan-list";
import { AccountPlanStatus } from "../types";
import { PATH } from "@/constants/path";
import Link from "next/link";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { usePathname } from "next/navigation";

function SearchAccountPlan() {
  const pathname = usePathname();

  const [search, setSearch] = useState("");
  const [isFocused, setIsFocused] = useState(false);

  const { accountPlanList } = useAccountPlanList({
    params: {
      disable_pagination: true,
      status: AccountPlanStatus.ACTIVE,
    },
  });

  if (pathname === PATH.DASHBOARD_ACCOUNT_PLAN) return null;

  return (
    <div className="relative w-fit">
      <Command
        className="w-[17.5vw]"
        filter={(value, search) =>
          value.toLowerCase().includes(search.toLowerCase()) ? 1 : 0
        }
      >
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger>
              <CommandInput
                value={search}
                onValueChange={setSearch}
                onFocus={() => setIsFocused(true)}
                onBlur={() =>
                  setTimeout(() => {
                    setIsFocused(false);
                  }, 100)
                }
                placeholder="Search Account Name"
              />
            </TooltipTrigger>
            <TooltipContent side="left">
              Type the name of an account to quickly locate it in the dashboard.
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
        {isFocused && (
          <div className="absolute left-0 top-full z-10 w-full rounded-b-md bg-white shadow-md">
            <CommandList>
              {accountPlanList.map((plan, idx) => (
                <CommandItem
                  key={idx}
                  className="bg-white"
                  value={`${plan.company} - ${plan.account_addressable_area}`}
                >
                  <Link
                    className="w-full"
                    href={PATH.DASHBOARD_ACCOUNT_PLAN_EDIT(plan.id)}
                  >
                    <p className="mr-[0.2vw]">
                      <strong>{plan.company}</strong> -{" "}
                      {plan.account_addressable_area}
                    </p>{" "}
                  </Link>
                </CommandItem>
              ))}
            </CommandList>
          </div>
        )}
      </Command>
    </div>
  );
}

export default SearchAccountPlan;
