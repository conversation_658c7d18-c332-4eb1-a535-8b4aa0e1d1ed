# Frontend API URL - REQUIRED for the app to function
# For local development (uses proxy to avoid CORS):
NEXT_PUBLIC_API_URL=http://localhost:3000/api/v1

# For Vercel deployment (direct backend connection):
# NEXT_PUBLIC_API_URL=https://psai-api.fly.dev/v1

# Backend URL for proxy (used in local development only)
# Use Pre-Production backend for development branch
PROXY_BACKEND_URL=https://psai-api.fly.dev

# Alternative backend URLs for different environments:
# Development: https://psai-api-wispy-resonance-3660.fly.dev
# Pre-Production: https://psai-api.fly.dev
# Production: https://api.perceptionselling.ai

# Development environment flag
NODE_ENV=development

# Optional: Sentry for error tracking
SENTRY_AUTH_TOKEN=